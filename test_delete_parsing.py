#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def test_delete_parsing():
    """测试删除命令的解析"""
    
    # 测试删除血糖
    test_cases_blood_sugar = [
        "删除血糖 1",
        "删除血糖 123",
        "删除血糖 5",
    ]
    
    print("删除血糖命令解析测试：")
    print("=" * 40)
    
    for test_case in test_cases_blood_sugar:
        if test_case.startswith("删除血糖"):
            try:
                record_id_str = test_case.replace("删除血糖", "").strip()
                record_id = int(record_id_str)
                print(f"输入: {test_case}")
                print(f"解析结果: ID = {record_id}")
                print("-" * 20)
            except ValueError as e:
                print(f"输入: {test_case}")
                print(f"解析错误: {e}")
                print("-" * 20)
    
    # 测试删除餐饮
    test_cases_meal = [
        "删除餐饮 今天 早餐",
        "删除餐饮 2025-06-27 午餐",
        "删除餐饮 昨天 晚餐",
    ]
    
    print("\n删除餐饮命令解析测试：")
    print("=" * 40)
    
    for test_case in test_cases_meal:
        if test_case.startswith("删除餐饮"):
            params_str = test_case.replace("删除餐饮", "").strip()
            parts = params_str.split()
            print(f"输入: {test_case}")
            print(f"解析结果: 参数 = {parts}")
            if len(parts) >= 2:
                print(f"日期: {parts[0]}, 餐饮类型: {parts[1]}")
            print("-" * 20)

if __name__ == "__main__":
    test_delete_parsing()
